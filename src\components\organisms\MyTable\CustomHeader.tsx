import {
  DeleteOutlined,
  PushpinFilled,
  PushpinOutlined,
} from "@ant-design/icons";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";

interface CustomHeaderProps {
  api: any;
  column: any;
  displayName: string;
  showFilter: (target: any) => void;
  onColumnsDelete?: () => void;
  hasEllipsis?: boolean;
  isWrapped?: boolean;
  onToggleWrap?: () => void;
}

const CustomHeaderBase = (props: CustomHeaderProps) => {
  const pinLeft = () => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), pinned: "left" }],
    });
  };

  const unpin = () => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), pinned: null }],
    });
  };

  const toggleFilter = (event) => {
    props.showFilter(event.target);
  };

  const handleSortIconClick = (sortOption) => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), sort: sortOption }],
      defaultState: { sort: null },
    });
    props.api.refreshHeader();
  };

  const handleWrapToggle = () => {
    console.log('Wrap toggle clicked for column:', props.column?.getColId(), {
      hasEllipsis: props.hasEllipsis,
      isWrapped: props.isWrapped,
      onToggleWrap: !!props.onToggleWrap
    });
    if (props.onToggleWrap) {
      props.onToggleWrap();
    }
  };

  const currentSort = props.column.getSort();

  return (
    <div
      className="custom-header-container"
      onClick={(e) => e.stopPropagation()}
    >
      <p>{props.displayName}</p>
      <div className="column-actions" style={{ color: "var(--color-text)" }}>
        {props?.column?.colDef?.withDelete && (
          <DeleteOutlined
            onClick={props.onColumnsDelete}
            style={{ color: "red", marginRight: 2 }}
          />
        )}
        {props.hasEllipsis && (
          <i
            className={`pi pi-align-justify text-wrap-toggle ${props.isWrapped ? "active" : ""
              }`}
            title={props.isWrapped ? "Truncate Text" : "Wrap Text"}
            onClick={handleWrapToggle}
          />
        )}
        {props?.column?.pinned ? (
          <PushpinFilled onClick={unpin} style={{ color: "red" }} />
        ) : (
          <PushpinOutlined onClick={pinLeft} />
        )}
        {currentSort === "asc" ? (
          <i
            className="pi pi-sort-amount-up-alt"
            title="Sort Ascending"
            onClick={() => handleSortIconClick("desc")}
            style={{ color: "red" }}
          />
        ) : currentSort === "desc" ? (
          <i
            className="pi pi-sort-amount-down-alt"
            title="Sort Ascending"
            onClick={() => handleSortIconClick(null)}
            style={{ color: "red" }}
          />
        ) : (
          <i
            className="pi pi-sort-alt"
            title="Sort"
            onClick={() => handleSortIconClick("asc")}
          ></i>
        )}
        <i
          className={`pi pi-filter`}
          title="Filter"
          onClick={toggleFilter}
          style={{
            cursor: "pointer",
            color: props.column.filterActive ? "red" : "var(--color-text)",
          }}
        ></i>
      </div>
    </div>
  );
};

export const CustomHeader = withErrorBoundary(
  React.memo(CustomHeaderBase),
  "error.generic"
);

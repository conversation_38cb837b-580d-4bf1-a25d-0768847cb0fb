.ag-header-container {
  background-color: var(--color-light);
  color: var(--color-text);
  font-size: 13px;
  font-weight: 700;
}

.ag-header-viewport {
  background-color: var(--color-light);
}

.ag-pinned-left-header {
  background-color: var(--color-light);
  color: var(--color-text);
}

.ag-body .title-container {
  cursor: pointer;
  color: var(--color-text);
}

.ag-body .ant-dropdown-trigger:hover {
  text-decoration: underline;
}

.custom-header-container {
  display: flex;
  width: 100%;
}

.custom-header-container>p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: gray;
  font-weight: 500;
}

.ag-cell {
  font-size: 12px;
}

.column-actions {
  display: flex;
  margin-left: auto;
  font-size: 14px;
  gap: 3px;
  align-items: center;
}

.column-actions i,
.column-actions span {
  cursor: pointer;
}

.ag-cell img {
  object-fit: contain;
}

/* Virtualization optimizations for all tables */
.ag-center-cols-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-horizontal-scroll {
  height: auto !important;
}

.ag-root-wrapper {
  overflow: hidden;
}

/* Ensure proper height calculation */
.ag-layout-normal {
  height: 100% !important;
}

/* Text wrapping control classes */
.ag-cell.text-truncated {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-cell.text-wrapped {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
  padding-top: 4px;
  padding-bottom: 4px;
}

/* Column-specific text wrapping classes */
.ag-cell[col-id].text-wrapped {
  height: auto !important;
  min-height: 38px;
}

/* Ensure wrapped cells have proper vertical alignment */
.ag-cell.text-wrapped .ag-cell-value {
  display: flex;
  align-items: flex-start;
  min-height: 30px;
  padding-top: 4px;
}

/* Text wrap toggle icon in header */
.text-wrap-toggle {
  cursor: pointer;
  color: var(--color-text);
  font-size: 14px;
  margin-right: 3px;
}

.text-wrap-toggle:hover {
  color: #1890ff;
}

.text-wrap-toggle.active {
  color: red;
}

/* Adjust row height for wrapped content */
.ag-row.has-wrapped-content {
  height: auto !important;
  min-height: 38px;
}

.ag-row.has-wrapped-content .ag-cell {
  height: auto !important;
  min-height: 38px;
  display: flex;
  align-items: flex-start;
}
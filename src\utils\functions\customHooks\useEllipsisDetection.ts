import { useEffect, useState, useRef, useCallback } from 'react';
import {
  getAGGridColumnCells,
  getPrimeReactColumnCells,
  isTextTruncated,
  debounce,
} from '../../ellipsisDetection';

export type TableType = 'ag-grid' | 'primereact';

export interface ColumnEllipsisState {
  [columnId: string]: boolean;
}

export interface UseEllipsisDetectionOptions {
  tableType: TableType;
  columns: Array<{ id: string; field: string }>;
  containerRef: React.RefObject<HTMLElement | HTMLDivElement>;
  enabled?: boolean;
  debounceMs?: number;
}

/**
 * Custom hook for detecting text ellipsis in table columns
 * Efficiently monitors column cells and returns which columns have truncated text
 */
export const useEllipsisDetection = ({
  tableType,
  columns,
  containerRef,
  enabled = true,
  debounceMs = 150,
}: UseEllipsisDetectionOptions) => {
  const [ellipsisState, setEllipsisState] = useState<ColumnEllipsisState>({});
  const observerRef = useRef<ResizeObserver | null>(null);
  const mutationObserverRef = useRef<MutationObserver | null>(null);
  const isInitializedRef = useRef(false);

  /**
   * Check ellipsis state for all columns
   */
  const checkEllipsisState = useCallback(() => {
    if (!enabled || !containerRef.current || columns.length === 0) {
      return;
    }

    const newEllipsisState: ColumnEllipsisState = {};

    columns.forEach(({ id, field }) => {
      let cells: HTMLElement[] = [];

      try {
        if (tableType === 'ag-grid') {
          cells = getAGGridColumnCells(field, containerRef.current!);
        } else if (tableType === 'primereact') {
          cells = getPrimeReactColumnCells(field, containerRef.current!);
        }

        // Check if any cell in the column has truncated text
        const hasEllipsis = cells.some(cell => isTextTruncated(cell));
        newEllipsisState[id] = hasEllipsis;
      } catch (error) {
        console.warn(`Error checking ellipsis for column ${id}:`, error);
        newEllipsisState[id] = false;
      }
    });

    // Only update state if there are actual changes
    setEllipsisState(prevState => {
      const hasChanges = Object.keys(newEllipsisState).some(
        key => prevState[key] !== newEllipsisState[key]
      );

      return hasChanges ? newEllipsisState : prevState;
    });
  }, [enabled, containerRef, columns, tableType]);

  /**
   * Debounced version of ellipsis check for performance
   */
  const debouncedCheck = useCallback(
    debounce(checkEllipsisState, debounceMs),
    [checkEllipsisState, debounceMs]
  );

  /**
   * Stable check that prevents rapid state changes
   */
  const stableCheck = useCallback(() => {
    // Add a small delay to ensure DOM is stable
    setTimeout(debouncedCheck, 50);
  }, [debouncedCheck]);

  /**
   * Setup ResizeObserver to monitor container size changes
   */
  const setupResizeObserver = useCallback(() => {
    if (!containerRef.current || !enabled) return;

    // Clean up existing observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new ResizeObserver(stableCheck);
    observerRef.current.observe(containerRef.current);
  }, [containerRef, enabled, debouncedCheck]);

  /**
   * Setup MutationObserver to monitor DOM changes (new rows, content changes)
   */
  const setupMutationObserver = useCallback(() => {
    if (!containerRef.current || !enabled) return;

    // Clean up existing observer
    if (mutationObserverRef.current) {
      mutationObserverRef.current.disconnect();
    }

    mutationObserverRef.current = new MutationObserver((mutations) => {
      let shouldCheck = false;

      mutations.forEach((mutation) => {
        // Check for added/removed nodes (new rows)
        if (mutation.type === 'childList' &&
          (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
          shouldCheck = true;
        }

        // Check for text content changes
        if (mutation.type === 'characterData') {
          shouldCheck = true;
        }

        // Check for attribute changes that might affect layout
        if (mutation.type === 'attributes' &&
          ['style', 'class'].includes(mutation.attributeName || '')) {
          shouldCheck = true;
        }
      });

      if (shouldCheck) {
        stableCheck();
      }
    });

    mutationObserverRef.current.observe(containerRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
      attributeFilter: ['style', 'class'],
    });
  }, [containerRef, enabled, stableCheck]);

  /**
   * Initialize observers and perform initial check
   */
  const initialize = useCallback(() => {
    if (!enabled || !containerRef.current || isInitializedRef.current) {
      return;
    }

    setupResizeObserver();
    setupMutationObserver();

    // Perform initial check after a short delay to ensure DOM is ready
    setTimeout(checkEllipsisState, 200);

    isInitializedRef.current = true;
  }, [enabled, containerRef, setupResizeObserver, setupMutationObserver, checkEllipsisState]);

  /**
   * Cleanup function
   */
  const cleanup = useCallback(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }

    if (mutationObserverRef.current) {
      mutationObserverRef.current.disconnect();
      mutationObserverRef.current = null;
    }

    isInitializedRef.current = false;
  }, []);

  /**
   * Manual refresh function for external triggers
   */
  const refresh = useCallback(() => {
    if (enabled) {
      checkEllipsisState();
    }
  }, [enabled, checkEllipsisState]);

  // Initialize when dependencies change
  useEffect(() => {
    if (enabled && containerRef.current && columns.length > 0) {
      initialize();
    } else {
      cleanup();
    }

    return cleanup;
  }, [enabled, containerRef, columns, initialize, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    ellipsisState,
    refresh,
    hasEllipsis: (columnId: string) => Boolean(ellipsisState[columnId]),
    getColumnsWithEllipsis: () =>
      Object.keys(ellipsisState).filter(columnId => ellipsisState[columnId]),
  };
};

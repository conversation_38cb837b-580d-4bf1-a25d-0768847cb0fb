/**
 * Utility functions for detecting text ellipsis in DOM elements
 */

/**
 * Checks if an element's text content is being truncated with ellipsis
 * @param element - The DOM element to check
 * @returns boolean - true if text is truncated, false otherwise
 */
export const isTextTruncated = (element: HTMLElement): boolean => {
  if (!element) return false;

  // Check if element has text content
  const textContent = element.textContent || element.innerText;
  if (!textContent || textContent.trim().length === 0) return false;

  // Get computed styles
  const computedStyle = window.getComputedStyle(element);
  const overflow = computedStyle.overflow;
  const textOverflow = computedStyle.textOverflow;
  const whiteSpace = computedStyle.whiteSpace;

  // Check if element is set up for ellipsis
  const hasEllipsisStyles =
    (overflow === 'hidden' || overflow === 'clip') &&
    textOverflow === 'ellipsis' &&
    (whiteSpace === 'nowrap' || whiteSpace === 'pre');

  if (!hasEllipsisStyles) return false;

  // Create a temporary element to measure the full text width
  const tempElement = document.createElement('span');
  tempElement.style.visibility = 'hidden';
  tempElement.style.position = 'absolute';
  tempElement.style.whiteSpace = 'nowrap';
  tempElement.style.fontSize = computedStyle.fontSize;
  tempElement.style.fontFamily = computedStyle.fontFamily;
  tempElement.style.fontWeight = computedStyle.fontWeight;
  tempElement.style.fontStyle = computedStyle.fontStyle;
  tempElement.style.letterSpacing = computedStyle.letterSpacing;
  tempElement.textContent = textContent;

  document.body.appendChild(tempElement);
  const fullTextWidth = tempElement.offsetWidth;
  document.body.removeChild(tempElement);

  // Compare with available width
  const availableWidth = element.offsetWidth;

  return fullTextWidth > availableWidth;
};

/**
 * Checks if any cell in a column has truncated text
 * @param columnSelector - CSS selector or class name to identify column cells
 * @param containerElement - Container element to search within (optional)
 * @returns boolean - true if any cell in the column has truncated text
 */
export const isColumnTextTruncated = (
  columnSelector: string,
  containerElement?: HTMLElement
): boolean => {
  const container = containerElement || document;
  const cells = container.querySelectorAll(columnSelector) as NodeListOf<HTMLElement>;

  if (cells.length === 0) return false;

  // Check if any cell in the column has truncated text
  for (let i = 0; i < cells.length; i++) {
    if (isTextTruncated(cells[i])) {
      return true;
    }
  }

  return false;
};

/**
 * Gets all columns that have truncated text
 * @param columnSelectors - Array of objects with column identifier and selector
 * @param containerElement - Container element to search within (optional)
 * @returns Array of column identifiers that have truncated text
 */
export const getColumnsWithTruncatedText = (
  columnSelectors: Array<{ columnId: string; selector: string }>,
  containerElement?: HTMLElement
): string[] => {
  const truncatedColumns: string[] = [];

  columnSelectors.forEach(({ columnId, selector }) => {
    if (isColumnTextTruncated(selector, containerElement)) {
      truncatedColumns.push(columnId);
    }
  });

  return truncatedColumns;
};

/**
 * Debounced version of ellipsis detection for performance
 * @param fn - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

/**
 * Creates a ResizeObserver to monitor element size changes for ellipsis detection
 * @param elements - Elements to observe
 * @param callback - Callback function when resize is detected
 * @returns ResizeObserver instance
 */
export const createEllipsisResizeObserver = (
  elements: HTMLElement[],
  callback: (truncatedElements: HTMLElement[]) => void
): ResizeObserver => {
  const debouncedCallback = debounce(() => {
    const truncatedElements = elements.filter(isTextTruncated);
    callback(truncatedElements);
  }, 100);

  const observer = new ResizeObserver(debouncedCallback);

  elements.forEach(element => {
    if (element) observer.observe(element);
  });

  return observer;
};

/**
 * AG Grid specific helper to get cell elements for a column
 * @param columnId - AG Grid column ID
 * @param gridContainer - AG Grid container element
 * @returns Array of cell elements for the column
 */
export const getAGGridColumnCells = (
  columnId: string,
  gridContainer: HTMLElement
): HTMLElement[] => {
  try {
    // Try multiple selectors for AG Grid cells
    let cells = gridContainer.querySelectorAll(
      `[col-id="${columnId}"] .ag-cell-value`
    ) as NodeListOf<HTMLElement>;

    // If no cells found, try alternative selector
    if (cells.length === 0) {
      cells = gridContainer.querySelectorAll(
        `.ag-cell[col-id="${columnId}"]`
      ) as NodeListOf<HTMLElement>;
    }

    // If still no cells, try data attribute selector
    if (cells.length === 0) {
      cells = gridContainer.querySelectorAll(
        `[data-colid="${columnId}"] .ag-cell-value`
      ) as NodeListOf<HTMLElement>;
    }

    return Array.from(cells);
  } catch (error) {
    console.warn(`Error getting AG Grid cells for column ${columnId}:`, error);
    return [];
  }
};

/**
 * PrimeReact DataTable specific helper to get cell elements for a column
 * @param columnKey - PrimeReact column key
 * @param tableContainer - DataTable container element
 * @returns Array of cell elements for the column
 */
export const getPrimeReactColumnCells = (
  columnKey: string,
  tableContainer: HTMLElement
): HTMLElement[] => {
  // PrimeReact uses data-field attribute for column identification
  const cells = tableContainer.querySelectorAll(
    `td[data-field="${columnKey}"]`
  ) as NodeListOf<HTMLElement>;

  return Array.from(cells);
};

import { useState, useCallback, useMemo } from 'react';

export interface ColumnWrapState {
  [columnId: string]: boolean;
}

export interface UseTextWrapStateOptions {
  initialWrapState?: ColumnWrapState;
  onWrapStateChange?: (wrapState: ColumnWrapState) => void;
}

/**
 * Custom hook for managing text wrap state for table columns
 * Tracks which columns are in wrapped vs truncated mode
 */
export const useTextWrapState = ({
  initialWrapState = {},
  onWrapStateChange,
}: UseTextWrapStateOptions = {}) => {
  const [wrapState, setWrapState] = useState<ColumnWrapState>(initialWrapState);

  /**
   * Toggle wrap state for a specific column
   */
  const toggleColumnWrap = useCallback((columnId: string) => {
    setWrapState(prevState => {
      const newState = {
        ...prevState,
        [columnId]: !prevState[columnId],
      };
      
      // Notify parent component of state change
      onWrapStateChange?.(newState);
      
      return newState;
    });
  }, [onWrapStateChange]);

  /**
   * Set wrap state for a specific column
   */
  const setColumnWrap = useCallback((columnId: string, isWrapped: boolean) => {
    setWrapState(prevState => {
      if (prevState[columnId] === isWrapped) {
        return prevState; // No change needed
      }
      
      const newState = {
        ...prevState,
        [columnId]: isWrapped,
      };
      
      // Notify parent component of state change
      onWrapStateChange?.(newState);
      
      return newState;
    });
  }, [onWrapStateChange]);

  /**
   * Set wrap state for multiple columns at once
   */
  const setMultipleColumnWrap = useCallback((updates: ColumnWrapState) => {
    setWrapState(prevState => {
      const newState = { ...prevState, ...updates };
      
      // Check if there are actual changes
      const hasChanges = Object.keys(updates).some(
        columnId => prevState[columnId] !== updates[columnId]
      );
      
      if (hasChanges) {
        onWrapStateChange?.(newState);
        return newState;
      }
      
      return prevState;
    });
  }, [onWrapStateChange]);

  /**
   * Reset wrap state for all columns
   */
  const resetWrapState = useCallback(() => {
    setWrapState({});
    onWrapStateChange?.({});
  }, [onWrapStateChange]);

  /**
   * Reset wrap state for specific columns
   */
  const resetColumnsWrapState = useCallback((columnIds: string[]) => {
    setWrapState(prevState => {
      const newState = { ...prevState };
      columnIds.forEach(columnId => {
        delete newState[columnId];
      });
      
      onWrapStateChange?.(newState);
      return newState;
    });
  }, [onWrapStateChange]);

  /**
   * Check if a column is wrapped
   */
  const isColumnWrapped = useCallback((columnId: string): boolean => {
    return Boolean(wrapState[columnId]);
  }, [wrapState]);

  /**
   * Get all wrapped columns
   */
  const getWrappedColumns = useMemo(() => {
    return Object.keys(wrapState).filter(columnId => wrapState[columnId]);
  }, [wrapState]);

  /**
   * Get CSS class name for a column based on wrap state
   */
  const getColumnWrapClass = useCallback((columnId: string): string => {
    return isColumnWrapped(columnId) ? 'text-wrapped' : 'text-truncated';
  }, [isColumnWrapped]);

  /**
   * Generate CSS classes for all columns
   */
  const getColumnWrapClasses = useMemo(() => {
    const classes: { [columnId: string]: string } = {};
    Object.keys(wrapState).forEach(columnId => {
      classes[columnId] = getColumnWrapClass(columnId);
    });
    return classes;
  }, [wrapState, getColumnWrapClass]);

  return {
    wrapState,
    toggleColumnWrap,
    setColumnWrap,
    setMultipleColumnWrap,
    resetWrapState,
    resetColumnsWrapState,
    isColumnWrapped,
    getWrappedColumns,
    getColumnWrapClass,
    getColumnWrapClasses,
  };
};
